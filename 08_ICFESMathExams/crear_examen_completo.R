# ============================================================================
# SCRIPT COMPLETO: CREAR PRIMER EXAMEN ICFES CON ICFESMathExams v3.0.0
# ============================================================================
# Basado en ejercicio de interpretación de gráficas de poblaciones de países
# Autor: ICFESMathExams v3.0.0
# Fecha: 2025-01-07
# ============================================================================

# Limpiar entorno
rm(list = ls())
cat("\014")  # Limpiar consola

# Función auxiliar para repetir caracteres
`%R%` <- function(char, n) paste(rep(char, n), collapse = "")

# ============================================================================
# PASO 1: CARGAR PAQUETE Y CONFIGURAR
# ============================================================================

cat("🚀 PASO 1: Cargando ICFESMathExams v3.0.0...\n")
cat("=" %R% 50, "\n")

# Cargar el paquete
library(ICFESMathExams)

# Verificar versión
cat("📦 Versión instalada:", as.character(packageVersion("ICFESMathExams")), "\n")

# Inicializar con configuración educativa
cat("🔧 Inicializando configuración...\n")
config <- inicializar_icfes_optimizado(modo_operacion = "educativo")

cat("✅ PASO 1 COMPLETADO\n\n")

# ============================================================================
# PASO 2: PROBAR NUEVO EJERCICIO DE INTERPRETACIÓN DE GRÁFICAS
# ============================================================================

cat("📊 PASO 2: Probando interpretación de gráficas...\n")
cat("=" %R% 50, "\n")

# Generar ejercicio basado en poblaciones de países
ejercicio_graficas <- generar_interpretacion_graficas("poblaciones_paises")

# Mostrar detalles del ejercicio
cat("=== EJERCICIO DE INTERPRETACIÓN DE GRÁFICAS ===\n")
cat("Tipo:", ejercicio_graficas$tipo, "\n")
cat("Competencia:", ejercicio_graficas$competencia, "\n")
cat("Nivel de dificultad:", ejercicio_graficas$nivel_dificultad, "\n\n")

cat("ENUNCIADO:\n")
cat(ejercicio_graficas$enunciado, "\n\n")

cat("PAÍSES INCLUIDOS:\n")
for (i in seq_along(ejercicio_graficas$paises)) {
  cat(sprintf("  %d. %s\n", i, ejercicio_graficas$paises[i]))
}

cat("\nOPCIONES DE RESPUESTA:\n")
for (i in seq_along(ejercicio_graficas$opciones)) {
  marca <- if (ejercicio_graficas$opciones[i] == ejercicio_graficas$respuesta_correcta) " ✓" else ""
  cat(sprintf("  %s) %d%s\n", LETTERS[i], ejercicio_graficas$opciones[i], marca))
}

cat("\nSOLUCIÓN:\n")
cat(ejercicio_graficas$solucion, "\n")

cat("\n✅ PASO 2 COMPLETADO\n\n")

# ============================================================================
# PASO 3: CREAR BANCO DE EJERCICIOS COMPLETO
# ============================================================================

cat("🏗️ PASO 3: Creando banco de ejercicios completo...\n")
cat("=" %R% 50, "\n")

# Generar diferentes tipos de ejercicios
cat("Generando ejercicios diversos...\n")

ejercicios_banco <- list()

# Ejercicios básicos mejorados
cat("  📐 Generando movimiento lineal...\n")
ejercicios_banco$movimiento <- generar_datos("movimiento_lineal")

cat("  📊 Generando estadística descriptiva...\n")
ejercicios_banco$estadistica <- generar_datos("estadistica_descriptiva")

# Nuevos ejercicios avanzados
cat("  🔺 Generando geometría analítica...\n")
ejercicios_banco$geometria <- generar_geometria_analitica("area_triangulo")

cat("  📈 Generando funciones matemáticas...\n")
ejercicios_banco$funciones <- generar_funciones_matematicas("funcion_compuesta")

cat("  🧮 Generando álgebra avanzada...\n")
ejercicios_banco$algebra <- generar_algebra_avanzada("multiplicacion_polinomios")

# NUEVO: Interpretación de gráficas (múltiples versiones)
cat("  📊 Generando interpretación de gráficas (3 versiones)...\n")
ejercicios_banco$graficas1 <- generar_interpretacion_graficas("poblaciones_paises")
ejercicios_banco$graficas2 <- generar_interpretacion_graficas("poblaciones_paises")
ejercicios_banco$graficas3 <- generar_interpretacion_graficas("poblaciones_paises")

cat("\n✅ Banco creado con", length(ejercicios_banco), "ejercicios:\n")

# Mostrar resumen
for (i in seq_along(ejercicios_banco)) {
  ejercicio <- ejercicios_banco[[i]]
  nombre <- names(ejercicios_banco)[i]
  cat(sprintf("  %d. %s (%s): %s...\n", 
              i, nombre, ejercicio$tipo, 
              substr(ejercicio$enunciado, 1, 50)))
}

cat("\n✅ PASO 3 COMPLETADO\n\n")

# ============================================================================
# PASO 4: CREAR TEMPLATES R-EXAMS
# ============================================================================

cat("📝 PASO 4: Creando templates r-exams...\n")
cat("=" %R% 50, "\n")

# Crear directorio de templates
dir_templates <- "templates_examen_completo"
if (!dir.exists(dir_templates)) {
  dir.create(dir_templates, showWarnings = FALSE)
  cat("📁 Directorio creado:", dir_templates, "\n")
}

# Crear templates para cada ejercicio
templates_creados <- c()
cat("Creando templates individuales...\n")

for (i in seq_along(ejercicios_banco)) {
  ejercicio <- ejercicios_banco[[i]]
  nombre_ejercicio <- names(ejercicios_banco)[i]
  
  # Crear template
  template_path <- crear_template_rexams(
    ejercicio,
    "single_choice",
    paste0(dir_templates, "/"),
    sprintf("ejercicio_%02d_%s", i, nombre_ejercicio)
  )
  
  templates_creados <- c(templates_creados, basename(template_path))
  cat(sprintf("  ✅ Template %d: %s\n", i, basename(template_path)))
}

cat("\n📁 Templates creados en:", dir_templates, "/\n")
cat("📄 Total de templates:", length(templates_creados), "\n")

cat("\n✅ PASO 4 COMPLETADO\n\n")

# ============================================================================
# PASO 5: CREAR EXAMEN ADAPTATIVO
# ============================================================================

cat("🎯 PASO 5: Creando examen adaptativo...\n")
cat("=" %R% 50, "\n")

# Definir perfil de estudiante colombiano
perfil_estudiante <- list(
  habilidad_estimada = 0.5,
  grado = 11,
  fortalezas = c("algebra", "geometria"),
  debilidades = c("estadistica", "interpretacion_graficas"),
  contexto = "urbano",
  region = "Cundinamarca"
)

cat("👤 Perfil del estudiante:\n")
cat("  - Grado:", perfil_estudiante$grado, "\n")
cat("  - Habilidad estimada:", perfil_estudiante$habilidad_estimada, "\n")
cat("  - Fortalezas:", paste(perfil_estudiante$fortalezas, collapse = ", "), "\n")
cat("  - Debilidades:", paste(perfil_estudiante$debilidades, collapse = ", "), "\n")
cat("  - Contexto:", perfil_estudiante$contexto, "\n")

# Generar examen adaptativo
cat("\n🔄 Generando examen adaptativo...\n")
examen_adaptativo <- generar_examen_adaptativo(
  perfil_estudiante,
  ejercicios_banco,
  num_ejercicios = 6,
  algoritmo_adaptacion = "competencias_balanceadas"
)

# Mostrar resultado
cat("\n=== EXAMEN ADAPTATIVO GENERADO ===\n")
print(examen_adaptativo)

cat("\n✅ PASO 5 COMPLETADO\n\n")

# ============================================================================
# PASO 6: EXPORTAR A MOODLE
# ============================================================================

cat("🌐 PASO 6: Exportando a Moodle...\n")
cat("=" %R% 50, "\n")

# Configuración específica para Moodle
config_moodle <- list(
  nombre_curso = "Matemáticas ICFES - Grado 11",
  categoria = "Interpretación de Gráficas y Competencias Matemáticas",
  tiempo_limite = 90,
  intentos_permitidos = 2,
  mostrar_retroalimentacion = TRUE,
  barajar_preguntas = TRUE,
  barajar_opciones = TRUE
)

cat("⚙️ Configuración Moodle:\n")
cat("  - Curso:", config_moodle$nombre_curso, "\n")
cat("  - Tiempo límite:", config_moodle$tiempo_limite, "minutos\n")
cat("  - Intentos permitidos:", config_moodle$intentos_permitidos, "\n")

# Crear directorio para Moodle
dir_moodle <- "examen_moodle"
if (!dir.exists(dir_moodle)) {
  dir.create(dir_moodle, showWarnings = FALSE)
}

# Exportar ejercicios del examen adaptativo
cat("\n📤 Exportando ejercicios a Moodle...\n")
ejercicios_para_moodle <- examen_adaptativo$ejercicios_seleccionados

archivos_moodle <- exportar_lms(
  ejercicios_para_moodle,
  formato_lms = "moodle",
  configuracion_exportacion = config_moodle,
  directorio_salida = paste0(dir_moodle, "/")
)

# Mostrar resultado
cat("\n=== EXPORTACIÓN MOODLE ===\n")
print(archivos_moodle)

cat("\n✅ PASO 6 COMPLETADO\n\n")

# ============================================================================
# PASO 7: CREAR EXAMEN R-EXAMS COMPLETO
# ============================================================================

cat("📊 PASO 7: Creando examen r-exams completo...\n")
cat("=" %R% 50, "\n")

# Configuración del examen completo
config_examen <- list(
  titulo = "Examen ICFES Matemáticas - Interpretación de Gráficas",
  instrucciones = "Lea cuidadosamente cada pregunta y seleccione la respuesta correcta. Preste especial atención a las gráficas presentadas.",
  tiempo_limite = 120,
  num_versiones = 5,
  formatos_salida = c("pdf", "html", "moodle"),
  incluir_solucionario = TRUE
)

cat("📋 Configuración del examen:\n")
cat("  - Título:", config_examen$titulo, "\n")
cat("  - Tiempo límite:", config_examen$tiempo_limite, "minutos\n")
cat("  - Versiones:", config_examen$num_versiones, "\n")
cat("  - Formatos:", paste(config_examen$formatos_salida, collapse = ", "), "\n")

# Crear examen completo
cat("\n🔄 Generando examen r-exams completo...\n")
examen_completo <- crear_examen_completo_rexams(
  ejercicios_banco,
  configuracion_examen = config_examen,
  directorio_templates = paste0(dir_templates, "/")
)

cat("\n=== EXAMEN R-EXAMS COMPLETO ===\n")
cat("✅ Templates:", length(examen_completo$templates), "\n")
cat("✅ Ejercicios:", examen_completo$num_ejercicios, "\n")
cat("✅ Tipos:", paste(unique(examen_completo$tipos_ejercicios), collapse = ", "), "\n")
cat("✅ Directorio:", examen_completo$directorio, "\n")

cat("\n✅ PASO 7 COMPLETADO\n\n")

# ============================================================================
# PASO 8: EXPORTAR A MÚLTIPLES FORMATOS
# ============================================================================

cat("📤 PASO 8: Exportando a múltiples formatos...\n")
cat("=" %R% 50, "\n")

# Crear directorio de salida
dir_output <- "output_examen_completo"
if (!dir.exists(dir_output)) {
  dir.create(dir_output, showWarnings = FALSE)
}

# Exportar a diferentes LMS
formatos_lms <- c("moodle", "canvas", "qti")
archivos_exportados <- list()

for (formato in formatos_lms) {
  cat(sprintf("📋 Exportando a %s...\n", toupper(formato)))

  tryCatch({
    archivos_formato <- exportar_lms(
      ejercicios_banco[1:5],  # Primeros 5 ejercicios
      formato_lms = formato,
      configuracion_exportacion = list(
        nombre_curso = paste("ICFES Matemáticas", toupper(formato)),
        categoria = "Exámenes Adaptativos"
      ),
      directorio_salida = paste0(dir_output, "/", formato, "/")
    )

    archivos_exportados[[formato]] <- archivos_formato
    cat(sprintf("  ✅ %s exportado exitosamente\n", toupper(formato)))

  }, error = function(e) {
    cat(sprintf("  ⚠️ Error exportando %s: %s\n", formato, e$message))
    archivos_exportados[[formato]] <- NULL
  })
}

cat("\n📁 Archivos exportados en:", dir_output, "/\n")

cat("\n✅ PASO 8 COMPLETADO\n\n")

# ============================================================================
# PASO 9: ANÁLISIS Y VALIDACIÓN
# ============================================================================

cat("🔍 PASO 9: Análisis y validación del examen...\n")
cat("=" %R% 50, "\n")

# Validar compatibilidad con r-exams
cat("🔍 Validando compatibilidad r-exams...\n")
validacion_rexams <- validar_compatibilidad_rexams(ejercicios_banco)

cat("=== VALIDACIÓN R-EXAMS ===\n")
cat("Compatible:", ifelse(validacion_rexams$compatible, "✅ SÍ", "❌ NO"), "\n")
cat("Ejercicios validados:", validacion_rexams$ejercicios_validados, "/", validacion_rexams$ejercicios_totales, "\n")

if (length(validacion_rexams$errores) > 0) {
  cat("Errores encontrados:\n")
  for (error in validacion_rexams$errores) {
    cat("  ❌", error, "\n")
  }
}

if (length(validacion_rexams$advertencias) > 0) {
  cat("Advertencias:\n")
  for (advertencia in validacion_rexams$advertencias) {
    cat("  ⚠️", advertencia, "\n")
  }
}

# Generar reporte de compatibilidad LMS
cat("\n🌐 Generando reporte de compatibilidad LMS...\n")
reporte_lms <- generar_reporte_compatibilidad_lms(
  ejercicios_banco[1:5],
  c("moodle", "canvas", "blackboard")
)

# Mostrar reporte manualmente para evitar errores de print
cat("\n=== REPORTE COMPATIBILIDAD LMS ===\n")
cat("Ejercicios analizados:", reporte_lms$resumen_general$num_ejercicios, "\n")
cat("LMS evaluados:", paste(reporte_lms$resumen_general$lms_analizados, collapse = ", "), "\n")

cat("\n--- Compatibilidad por LMS ---\n")
for (lms in names(reporte_lms$compatibilidad_por_lms)) {
  compat <- reporte_lms$compatibilidad_por_lms[[lms]]
  if (!is.null(compat) && !is.null(compat$porcentaje_compatibilidad)) {
    estado <- if (compat$porcentaje_compatibilidad >= 80) "Compatible" else "Problemas"
    cat(toupper(lms), ": ", estado, " (", round(compat$porcentaje_compatibilidad, 1), "%)\n", sep = "")
  } else {
    cat(toupper(lms), ": Error en análisis\n", sep = "")
  }
}

cat("\n--- Recomendaciones ---\n")
if (length(reporte_lms$recomendaciones) > 0) {
  for (rec in reporte_lms$recomendaciones) {
    cat("• ", rec, "\n", sep = "")
  }
} else {
  cat("• Excelente compatibilidad general\n")
}

cat("\n✅ PASO 9 COMPLETADO\n\n")

# ============================================================================
# PASO 10: RESUMEN FINAL Y INSTRUCCIONES
# ============================================================================

cat("🎉 PASO 10: Resumen final del examen completo\n")
cat("=" %R% 50, "\n")

# Estadísticas finales
total_ejercicios <- length(ejercicios_banco)
total_templates <- length(templates_creados)
total_formatos <- length(archivos_exportados)

cat("🎉 ¡EXAMEN COMPLETO CREADO EXITOSAMENTE!\n")
cat("==========================================\n\n")

cat("📊 ESTADÍSTICAS FINALES:\n")
cat("  📝 Ejercicios generados:", total_ejercicios, "\n")
cat("  📄 Templates r-exams:", total_templates, "\n")
cat("  🎯 Examen adaptativo: ✓\n")
cat("  🌐 Formatos LMS:", total_formatos, "\n")
cat("  📤 Exportaciones realizadas: ✓\n")
cat("  🔍 Validaciones completadas: ✓\n\n")

cat("📁 ARCHIVOS Y DIRECTORIOS CREADOS:\n")
cat("  📂", dir_templates, "/ (Templates r-exams)\n")
cat("  📂", dir_moodle, "/ (Archivos para Moodle)\n")
cat("  📂", dir_output, "/ (Exportaciones múltiples)\n\n")

cat("🚀 TU EXAMEN ESTÁ LISTO PARA USAR EN:\n")
cat("  ✅ Moodle (importar archivo XML)\n")
cat("  ✅ Canvas (usar archivos QTI)\n")
cat("  ✅ Blackboard (importar ZIP)\n")
cat("  ✅ PDF impreso (generar desde templates)\n")
cat("  ✅ HTML interactivo\n")
cat("  ✅ Examen adaptativo personalizado\n\n")

cat("📋 PRÓXIMOS PASOS SUGERIDOS:\n")
cat("  1. Revisar templates en:", dir_templates, "/\n")
cat("  2. Importar XML de Moodle desde:", dir_moodle, "/\n")
cat("  3. Personalizar configuraciones según necesidades\n")
cat("  4. Generar más versiones con diferentes perfiles\n")
cat("  5. Implementar análisis psicométrico con datos reales\n\n")

# Mostrar configuración actual
cat("⚙️ CONFIGURACIÓN ACTUAL:\n")
config_actual <- obtener_configuracion()
cat("  - Modo operación:", config_actual$modo_operacion, "\n")
cat("  - Tipos disponibles:", length(config_actual$generacion$tipos_ejercicios_disponibles), "\n")
cat("  - Formatos exportación:", length(config_actual$rexams$formatos_exportacion), "\n")
cat("  - Cache habilitado:", config_actual$generacion$cache_ejercicios, "\n\n")

cat("🎓 ¡FELICITACIONES! Has creado tu primer examen completo con ICFESMathExams v3.0.0\n")
cat("   Basado en interpretación de gráficas de poblaciones de países\n")
cat("   Compatible con estándares ICFES y r-exams\n\n")

cat("=" %R% 70, "\n")
cat("SCRIPT COMPLETADO EXITOSAMENTE - ", Sys.time(), "\n")
cat("=" %R% 70, "\n")
